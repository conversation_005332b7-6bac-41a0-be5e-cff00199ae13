from flask import Flask, render_template, request, jsonify, send_file
import googlemaps
import pandas as pd
import os
import io
from datetime import datetime
import requests
import json

app = Flask(__name__)

class GoogleMapsScraper:
    def __init__(self, api_key):
        self.gmaps = googlemaps.Client(key=api_key)
    
    def search_places(self, location, query, max_results=20):
        """
        البحث عن الأماكن في Google Maps
        """
        try:
            # البحث عن الأماكن
            places_result = self.gmaps.places_nearby(
                location=location,
                keyword=query,
                radius=5000  # 5 كيلومتر
            )
            
            results = []
            count = 0
            
            for place in places_result.get('results', []):
                if count >= max_results:
                    break
                
                place_details = self.get_place_details(place['place_id'])
                if place_details:
                    results.append(place_details)
                    count += 1
            
            return results
        
        except Exception as e:
            print(f"خطأ في البحث: {e}")
            return []
    
    def get_place_details(self, place_id):
        """
        الحصول على تفاصيل المكان
        """
        try:
            details = self.gmaps.place(
                place_id=place_id,
                fields=['name', 'formatted_phone_number', 'website', 
                       'formatted_address', 'rating', 'user_ratings_total',
                       'opening_hours', 'geometry']
            )
            
            result = details.get('result', {})
            
            # استخراج معلومات Facebook من الموقع الإلكتروني
            facebook_url = self.extract_facebook_from_website(result.get('website', ''))
            
            return {
                'name': result.get('name', 'غير متوفر'),
                'phone': result.get('formatted_phone_number', 'غير متوفر'),
                'address': result.get('formatted_address', 'غير متوفر'),
                'website': result.get('website', 'غير متوفر'),
                'facebook': facebook_url,
                'rating': result.get('rating', 'غير متوفر'),
                'reviews_count': result.get('user_ratings_total', 'غير متوفر'),
                'latitude': result.get('geometry', {}).get('location', {}).get('lat', ''),
                'longitude': result.get('geometry', {}).get('location', {}).get('lng', '')
            }
        
        except Exception as e:
            print(f"خطأ في الحصول على التفاصيل: {e}")
            return None
    
    def extract_facebook_from_website(self, website_url):
        """
        محاولة استخراج رابط Facebook من الموقع الإلكتروني
        """
        if not website_url or website_url == 'غير متوفر':
            return 'غير متوفر'
        
        try:
            # محاولة البحث عن روابط Facebook في الموقع
            response = requests.get(website_url, timeout=5)
            content = response.text.lower()
            
            # البحث عن روابط Facebook
            if 'facebook.com' in content:
                import re
                facebook_pattern = r'https?://(?:www\.)?facebook\.com/[^\s"\'<>]+'
                matches = re.findall(facebook_pattern, content)
                if matches:
                    return matches[0]
            
            return 'غير متوفر'
        
        except:
            return 'غير متوفر'

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/search', methods=['POST'])
def search():
    try:
        data = request.get_json()
        api_key = data.get('api_key')
        location = data.get('location')
        query = data.get('query', '')
        max_results = int(data.get('max_results', 20))
        
        if not api_key or not location:
            return jsonify({'error': 'API Key والموقع مطلوبان'}), 400
        
        # إنشاء كائن المستخرج
        scraper = GoogleMapsScraper(api_key)
        
        # البحث عن الأماكن
        results = scraper.search_places(location, query, max_results)
        
        return jsonify({'results': results, 'count': len(results)})
    
    except Exception as e:
        return jsonify({'error': f'خطأ في البحث: {str(e)}'}), 500

@app.route('/export_csv', methods=['POST'])
def export_csv():
    try:
        data = request.get_json()
        results = data.get('results', [])
        
        if not results:
            return jsonify({'error': 'لا توجد بيانات للتصدير'}), 400
        
        # إنشاء DataFrame
        df = pd.DataFrame(results)
        
        # إنشاء ملف CSV في الذاكرة
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8-sig')
        output.seek(0)
        
        # تحويل إلى bytes
        csv_data = output.getvalue().encode('utf-8-sig')
        
        # إنشاء اسم الملف مع التاريخ والوقت
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"google_maps_data_{timestamp}.csv"
        
        return send_file(
            io.BytesIO(csv_data),
            mimetype='text/csv',
            as_attachment=True,
            download_name=filename
        )
    
    except Exception as e:
        return jsonify({'error': f'خطأ في التصدير: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
