<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستخرج بيانات Google Maps</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ مستخرج بيانات Google Maps</h1>
            <p>استخرج بيانات الأعمال من Google Maps بسهولة</p>
        </div>

        <div class="main-card">
            <form id="searchForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="api_key">🔑 Google Maps API Key:</label>
                            <input type="password" id="api_key" class="form-control" 
                                   placeholder="أدخل API Key الخاص بك" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="location">📍 الموقع:</label>
                            <input type="text" id="location" class="form-control" 
                                   placeholder="مثال: الرياض، السعودية" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="query">🔍 نوع البحث (اختياري):</label>
                            <input type="text" id="query" class="form-control" 
                                   placeholder="مثال: مطاعم، صيدليات، محلات">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="max_results">📊 عدد النتائج:</label>
                            <select id="max_results" class="form-control">
                                <option value="10">10 نتائج</option>
                                <option value="20" selected>20 نتيجة</option>
                                <option value="50">50 نتيجة</option>
                                <option value="100">100 نتيجة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="btn btn-primary" id="searchBtn">
                        🔍 بدء البحث
                    </button>
                </div>
            </form>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري البحث... يرجى الانتظار</p>
            </div>

            <div id="error-message"></div>
        </div>

        <div class="main-card results-section" id="resultsSection">
            <div class="results-header">
                <div class="results-count" id="resultsCount">
                    تم العثور على 0 نتيجة
                </div>
                <button class="btn btn-success" id="exportBtn" onclick="exportToCSV()">
                    📥 تصدير إلى CSV
                </button>
            </div>

            <div style="overflow-x: auto;">
                <table class="results-table" id="resultsTable">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>رقم الهاتف</th>
                            <th>العنوان</th>
                            <th>الموقع الإلكتروني</th>
                            <th>Facebook</th>
                            <th>التقييم</th>
                            <th>عدد المراجعات</th>
                        </tr>
                    </thead>
                    <tbody id="resultsBody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        let searchResults = [];

        document.getElementById('searchForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const apiKey = document.getElementById('api_key').value;
            const location = document.getElementById('location').value;
            const query = document.getElementById('query').value;
            const maxResults = document.getElementById('max_results').value;

            // إظهار التحميل
            document.getElementById('loading').style.display = 'block';
            document.getElementById('searchBtn').disabled = true;
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('error-message').innerHTML = '';

            try {
                const response = await fetch('/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        api_key: apiKey,
                        location: location,
                        query: query,
                        max_results: maxResults
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    searchResults = data.results;
                    displayResults(data.results, data.count);
                } else {
                    showError(data.error);
                }
            } catch (error) {
                showError('حدث خطأ في الاتصال: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('searchBtn').disabled = false;
            }
        });

        function displayResults(results, count) {
            document.getElementById('resultsCount').textContent = `تم العثور على ${count} نتيجة`;
            
            const tbody = document.getElementById('resultsBody');
            tbody.innerHTML = '';

            results.forEach(result => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${result.name}</td>
                    <td>${result.phone}</td>
                    <td>${result.address}</td>
                    <td>${result.website !== 'غير متوفر' ? `<a href="${result.website}" target="_blank">زيارة الموقع</a>` : 'غير متوفر'}</td>
                    <td>${result.facebook !== 'غير متوفر' ? `<a href="${result.facebook}" target="_blank">Facebook</a>` : 'غير متوفر'}</td>
                    <td>${result.rating}</td>
                    <td>${result.reviews_count}</td>
                `;
            });

            document.getElementById('resultsSection').style.display = 'block';
        }

        function showError(message) {
            document.getElementById('error-message').innerHTML = `
                <div class="alert alert-danger">
                    ❌ ${message}
                </div>
            `;
        }

        async function exportToCSV() {
            if (searchResults.length === 0) {
                showError('لا توجد بيانات للتصدير');
                return;
            }

            try {
                const response = await fetch('/export_csv', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        results: searchResults
                    })
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = `google_maps_data_${new Date().getTime()}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    
                    document.getElementById('error-message').innerHTML = `
                        <div class="alert alert-success">
                            ✅ تم تصدير البيانات بنجاح!
                        </div>
                    `;
                } else {
                    const data = await response.json();
                    showError(data.error);
                }
            } catch (error) {
                showError('حدث خطأ في التصدير: ' + error.message);
            }
        }
    </script>
</body>
</html>
