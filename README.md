# مستخرج بيانات Google Maps

برنامج Flask لاستخراج بيانات الأعمال من Google Maps مع إمكانية التصدير إلى CSV.

## المميزات

- 🔍 البحث عن الأعمال في مواقع محددة
- 📱 استخراج أرقام الهواتف
- 🌐 استخراج المواقع الإلكترونية
- 📘 البحث عن روابط Facebook
- ⭐ عرض التقييمات وعدد المراجعات
- 📊 تصدير البيانات إلى ملف CSV
- 🎨 واجهة مستخدم عربية جميلة

## المتطلبات

- Python 3.7+
- Google Maps API Key

## التثبيت

1. استنساخ المشروع:
```bash
git clone <repository-url>
cd map-Scraper
```

2. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

3. الحصول على Google Maps API Key:
   - اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
   - أنشئ مشروع جديد أو اختر مشروع موجود
   - فعّل Google Maps API و Places API
   - أنشئ API Key

## التشغيل

1. تشغيل التطبيق:
```bash
python app.py
```

2. افتح المتصفح واذهب إلى:
```
http://localhost:5000
```

## الاستخدام

1. أدخل Google Maps API Key
2. حدد الموقع المراد البحث فيه
3. اختر نوع البحث (اختياري)
4. حدد عدد النتائج المطلوبة
5. اضغط "بدء البحث"
6. بعد ظهور النتائج، يمكنك تصديرها إلى CSV

## البيانات المستخرجة

- اسم العمل
- رقم الهاتف
- العنوان
- الموقع الإلكتروني
- رابط Facebook (إن وجد)
- التقييم
- عدد المراجعات
- الإحداثيات الجغرافية

## ملاحظات مهمة

- تأكد من أن API Key لديك صالح ومفعل
- قد تحتاج إلى تفعيل الفوترة في Google Cloud للحصول على نتائج أكثر
- احترم حدود الاستخدام لـ Google Maps API
- البرنامج يحاول استخراج روابط Facebook من المواقع الإلكترونية

## الدعم

إذا واجهت أي مشاكل، تأكد من:
- صحة API Key
- تفعيل Google Maps API و Places API
- وجود اتصال بالإنترنت
- صحة اسم الموقع المدخل
